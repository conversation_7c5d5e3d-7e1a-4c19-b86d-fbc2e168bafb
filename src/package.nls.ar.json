{"extension.displayName": "Kilo Code وكيل ذكاء اصطناعي (ميزات Cline و Roo مجتمعين)", "extension.description": "مساعد برمجة ذكاء اصطناعي مفتوح المصدر يساعدك تخطط وتبني وتعدل الكود.", "views.contextMenu.label": "Kilo Code", "views.terminalMenu.label": "Kilo Code", "views.activitybar.title": "Kilo Code (⇧⌘A)", "views.sidebar.name": "Kilo Code", "command.newTask.title": "مهمة جديدة", "command.mcpServers.title": "خوادم MCP", "command.prompts.title": "الأنماط المخصصة", "command.history.title": "السجل السابق", "command.marketplace.title": "السوق", "command.openInEditor.title": "افتح في المحرر", "command.settings.title": "الإعدادات", "command.documentation.title": "التوثيق", "command.openInNewTab.title": "افتح في تبويب جديد", "command.explainCode.title": "اشر<PERSON> الكود", "command.fixCode.title": "ص<PERSON><PERSON> الكود", "command.improveCode.title": "حسّن الكود", "command.addToContext.title": "أ<PERSON><PERSON> السياق", "command.focusInput.title": "ركّز على حقل الإدخال", "command.setCustomStoragePath.title": "حدد مسار تخزين مخصص", "command.importSettings.title": "استورد الإعدادات", "command.terminal.addToContext.title": "إضافة ناتج الطرفية إلى السياق الحالي", "command.terminal.fixCommand.title": "صلح هذا الأمر", "command.terminal.explainCommand.title": "شرح هذا الأمر", "command.acceptInput.title": "اعتماد الإدخال أو الاقتراح", "command.generateCommitMessage.title": "توليد رسالة الاعتماد بواسطة Kilo", "command.profile.title": "الملف الشخصي", "configuration.title": "Kilo Code", "commands.allowedCommands.description": "الأوامر اللي تنفّذ تلقائي إذا فعّلت خيار 'الموافقة دايمًا على تنفيذ العمليات'", "commands.deniedCommands.description": "بادئات الأوامر اللي ترفض تلقائيًا بدون طلب موافقة. في حالة التعارض مع الأوامر المسموحة، أطول بادئة متطابقة لها الأولوية. أضف * لرفض جميع الأوامر.", "commands.commandExecutionTimeout.description": "الحد الأقصى للوقت بالثواني لانتظار اكتمال تنفيذ الأمر قبل انتهاء المهلة (0 = بدون مهلة، 1-600 ثانية، افتراضي: 0 ثانية)", "settings.vsCodeLmModelSelector.description": "إعدادات واجهة برمجة نموذج اللغة في VSCode", "settings.vsCodeLmModelSelector.vendor.description": "مزود نموذج اللغة (مثال: copilot)", "settings.vsCodeLmModelSelector.family.description": "عائلة نموذج اللغة (مثال: gpt-4)", "settings.customStoragePath.description": "مسار تخزين مخصص. خله فاضي عشان يستخدم المسار الافتراضي. يدعم المسارات المطلقة (مثال: 'D:\\KiloCodeStorage')", "settings.enableCodeActions.description": "فعل الإصلاحات السريعة في Kilo Code", "settings.autoImportSettingsPath.description": "مسار ملف تكوين Kilo Code للاستيراد التلقائي عند بدء تشغيل الإضافة. يدعم المسارات المطلقة والمسارات النسبية لدليل المنزل (مثل '~/Documents/kilo-code-settings.json'). اتركه فارغًا لتعطيل الاستيراد التلقائي.", "ghost.input.title": "اضغط 'Enter' للتأكيد أو 'Escape' للإلغاء", "ghost.input.placeholder": "صف ما تريد فعله...", "ghost.commands.generateSuggestions": "Kilo Code: إنشاء التعديلات المقترحة", "ghost.commands.displaySuggestions": "عرض التعديلات المقترحة", "ghost.commands.cancelSuggestions": "إلغاء التعديلات المقترحة", "ghost.commands.applyCurrentSuggestion": "تطبيق التعديل المقترح الحالي", "ghost.commands.applyAllSuggestions": "تطبيق جميع التعديلات المقترحة", "ghost.commands.promptCodeSuggestion": "مهمة سريعة", "ghost.commands.goToNextSuggestion": "انتقل إلى الاقتراح التالي", "ghost.commands.goToPreviousSuggestion": "انتقل إلى الاقتراح السابق"}